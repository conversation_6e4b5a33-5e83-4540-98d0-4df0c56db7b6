# Email Verification Setup Guide

## Vấn đề đã được giải quyết

### 🔍 **Nguyên nhân email không được gửi:**

1. **Queue Configuration**: Email notifications được queue trong database nhưng không có worker chạy
2. **TrackableEmail Trait**: Có conflict với EmailTracking module gây lỗi trong queue processing

### ✅ **Giải pháp đã áp dụng:**

1. **Fixed Queue Processing**:
   - Emails được queue trong `jobs` table
   - Cần chạy `php artisan queue:work` để xử lý queue
   - Hoặc set `QUEUE_CONNECTION=sync` trong .env để gửi ngay lập tức

2. **Fixed TrackableEmail Integration**:
   - Thêm error handling cho EmailTracking module
   - Email vẫn gửi được ngay cả khi tracking fail
   - Tracking được thêm tự động nếu module có sẵn

## C<PERSON>u hình hiện tại

### Email Configuration (.env)
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=pgsqacpipndhejtq
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Maid Profit"
```

### Queue Configuration (.env)
```env
QUEUE_CONNECTION=database  # Emails được queue
# Hoặc
QUEUE_CONNECTION=sync      # Emails gửi ngay lập tức
```

## Cách sử dụng

### Option 1: Queue Processing (Recommended for Production)
```bash
# Chạy queue worker để xử lý emails
php artisan queue:work

# Hoặc chạy một lần
php artisan queue:work --once

# Kiểm tra jobs trong queue
php artisan queue:monitor
```

### Option 2: Synchronous Processing (Development)
```env
# Trong .env file
QUEUE_CONNECTION=sync
```

## Testing

### 1. Test API Endpoint
```bash
curl -X POST http://localhost:8000/api/send-verification-email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

### 2. Test Queue Processing
```bash
# Kiểm tra jobs trong queue
php artisan tinker --execute="echo 'Jobs in queue: ' . DB::table('jobs')->count();"

# Xử lý queue
php artisan queue:work --once
```

### 3. Test Email Configuration
```bash
php artisan tinker --execute="
try { 
  Mail::raw('Test email', function(\$message) { 
    \$message->to('<EMAIL>')->subject('Test Email'); 
  }); 
  echo 'Email sent successfully'; 
} catch (Exception \$e) { 
  echo 'Email failed: ' . \$e->getMessage(); 
}"
```

## Troubleshooting

### Email không được gửi
1. **Kiểm tra queue**: `php artisan tinker --execute="echo DB::table('jobs')->count();"`
2. **Chạy queue worker**: `php artisan queue:work --once`
3. **Kiểm tra failed jobs**: `php artisan queue:failed`
4. **Clear queue nếu cần**: `php artisan queue:clear`

### Gmail SMTP Issues
1. **App Password**: Đảm bảo sử dụng App Password thay vì password thường
2. **2FA**: Bật 2-Factor Authentication cho Gmail account
3. **Less Secure Apps**: Không cần thiết khi dùng App Password

### Queue Worker for Production
```bash
# Sử dụng supervisor hoặc systemd để chạy queue worker
php artisan queue:work --daemon --tries=3 --timeout=60
```

## API Endpoints

- `POST /api/send-verification-email` - Gửi email verification
- `GET /api/verify-email` - Xác thực email với token  
- `POST /api/resend-verification-email` - Gửi lại email verification

## Files Modified

1. `app/Notifications/EmailVerificationNotification.php` - Fixed TrackableEmail integration
2. `app/Http/Controllers/API/AuthController.php` - Added email verification methods
3. `app/Models/User.php` - Added email verification methods
4. `routes/api.php` - Added email verification routes

## Next Steps

1. **Production Setup**: Configure queue worker với supervisor/systemd
2. **Monitoring**: Set up queue monitoring và alerting
3. **Email Templates**: Customize email templates trong database
4. **Rate Limiting**: Thêm rate limiting cho email verification endpoints
