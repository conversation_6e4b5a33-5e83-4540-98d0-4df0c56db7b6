# Email Verification API Documentation

## Overview

This document describes the email verification system implemented for the JobON application. The system provides secure email verification functionality with automatic email sending, token-based verification, and comprehensive error handling.

## Features

- ✅ Automatic email verification after user registration
- ✅ Manual email verification sending
- ✅ Token-based verification with expiration (24 hours)
- ✅ Email tracking integration
- ✅ Multi-language support
- ✅ Both API and web interface support
- ✅ Middleware for protecting routes that require verified emails

## Database Changes

### New Fields Added to `users` Table

```sql
email_verification_token VARCHAR(255) NULL
email_verification_expires_at TIMESTAMP NULL
```

## API Endpoints

### 1. Send Verification Email

**Endpoint:** `POST /api/send-verification-email`

**Description:** Sends a verification email to the specified email address.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response (Success):**

```json
{
  "message": "Verification email has been sent to your email address.",
  "success": true
}
```

**Response (Error):**

```json
{
  "message": "Email address is already verified.",
  "success": false
}
```

### 2. Verify Email

**Endpoint:** `GET /api/verify-email`

**Description:** Verifies an email address using the verification token.

**Query Parameters:**

- `token` (required): The verification token
- `email` (required): The email address to verify

**Example:** `/api/verify-email?token=abc123&email=<EMAIL>`

**Response (Success - API):**

```json
{
  "message": "Email verified successfully.",
  "success": true,
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "email_verified_at": "2025-07-26T20:54:08.000000Z"
  }
}
```

**Response (Success - Web):**
Returns an HTML page showing verification success.

### 3. Resend Verification Email

**Endpoint:** `POST /api/resend-verification-email`

**Description:** Resends a verification email (generates new token).

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response (Success):**

```json
{
  "message": "Verification email has been resent to your email address.",
  "success": true
}
```

## User Model Methods

### New Methods Added

```php
// Generate verification token
public function generateEmailVerificationToken(): string

// Check if token is valid
public function isValidEmailVerificationToken(string $token): bool

// Mark email as verified
public function markEmailAsVerified(): bool

// Check if email is verified
public function hasVerifiedEmail(): bool

// Check if token has expired
public function emailVerificationTokenExpired(): bool
```

## Middleware

### EnsureEmailIsVerified

**Usage:** Apply to routes that require verified emails.

```php
Route::middleware(['auth:api', 'verified'])->group(function () {
    // Protected routes here
});
```

**Response when email not verified:**

```json
{
  "success": false,
  "error": {
    "code": "EMAIL_NOT_VERIFIED",
    "message": "Your email address is not verified. Please verify your email to continue.",
    "email_verification_required": true
  },
  "data": {
    "user_email": "<EMAIL>",
    "verification_endpoints": {
      "send_verification": "/api/send-verification-email",
      "resend_verification": "/api/resend-verification-email"
    }
  }
}
```

## Email Template

The system uses a customizable email template stored in the database with the slug `email-verification`. The template supports:

- Multi-language content (English and Vietnamese)
- Dynamic placeholders: `{{user_name}}`, `{{verification_url}}`, `{{company_name}}`
- Email tracking integration

## Registration Flow Changes

Both `register` and `registerProvider` methods now automatically:

1. Generate verification token
2. Send verification email
3. Return additional response fields:
   - `email_verification_sent: true`
   - `message: "Registration successful. Please check your email to verify your account."`

## Error Codes and Messages

| Error Code                 | Message                            | HTTP Status |
| -------------------------- | ---------------------------------- | ----------- |
| EMAIL_NOT_VERIFIED         | Your email address is not verified | 403         |
| EMAIL_ALREADY_VERIFIED     | Email address is already verified  | 400         |
| INVALID_VERIFICATION_TOKEN | Invalid verification token         | 400         |
| VERIFICATION_TOKEN_EXPIRED | Verification token has expired     | 400         |
| USER_NOT_EXISTS            | This user not exists or deactivate | 404         |

## Security Features

- Tokens are generated using HMAC-SHA256 with app key
- Tokens expire after 24 hours
- Tokens are single-use (cleared after verification)
- Email tracking excludes Super Admin users

## Testing

To test the email verification system:

1. Register a new user
2. Check that verification email is sent
3. Use the verification link from email
4. Verify that user's `email_verified_at` is set
5. Test middleware protection on routes

## Integration Notes

- The system integrates with the existing EmailTracking module
- Email templates are stored in the `email_templates` table
- The verification URL format: `/api/verify-email?token={token}&email={email}`
- Supports both JSON API responses and HTML web responses

## Troubleshooting

### Common Issues

1. **TrackableEmail trait not found**

   - Make sure to import: `use Modules\EmailTracking\Services\TrackableEmail;`
   - NOT: `use Modules\EmailTracking\Traits\TrackableEmail;`

2. **API endpoints not accessible**

   - Email verification endpoints are public (no auth required)
   - Routes: `/api/send-verification-email`, `/api/verify-email`, `/api/resend-verification-email`

3. **Token validation fails**
   - Tokens expire after 24 hours
   - Tokens are single-use (cleared after successful verification)
   - Generate new token if needed
