<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin-top: 1rem;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        @if($success)
            <div class="icon success">✓</div>
            <h1 class="success">Email Verified Successfully!</h1>
            <p>Your email address has been verified. You can now access all features of {{ config('app.name') }}.</p>
            <a href="{{ config('app.url') }}" class="btn">Continue to App</a>
        @else
            <div class="icon error">✗</div>
            <h1 class="error">Email Verification Failed</h1>
            <p>{{ $message ?? 'The verification link is invalid or has expired.' }}</p>
            <p>Please request a new verification email or contact support if the problem persists.</p>
            <a href="{{ config('app.url') }}" class="btn">Back to App</a>
        @endif
    </div>
</body>
</html>
