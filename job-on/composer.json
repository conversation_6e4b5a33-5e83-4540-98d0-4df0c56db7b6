{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1|^8.2", "barryvdh/laravel-dompdf": "^2.0", "cboden/ratchet": "0.4.x-dev", "cviebrock/eloquent-sluggable": "^11.0", "darkaonline/l5-swagger": "^9.0", "doctrine/dbal": "^4.2", "google/apiclient": "^2.15.0", "guzzlehttp/guzzle": "^7.5.0", "iyzico/iyzipay-php": "^2.0", "jackiedo/dotenv-editor": "*", "kreait/firebase-php": "^7.0", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.16", "laravel/tinker": "^2.9.0", "laravel/ui": "^4.5.0", "laravesl/phpunit": "dev-main", "league/flysystem-aws-s3-v3": "^3.29", "maatwebsite/excel": "^3.1.48", "matanyadaev/laravel-eloquent-spatial": "*", "mollie/laravel-mollie": "^3.0", "mongodb/laravel-mongodb": "^5.4", "nwidart/laravel-modules": "^10.0", "php-open-source-saver/jwt-auth": "^2.8", "phpoffice/phpspreadsheet": "*", "prettus/l5-repository": "^2.9", "psr/simple-cache": "^2.0", "ralphjsmit/laravel-seo": "^1.6", "razorpay/razorpay": "^2.9.0", "react/http": "^1.11", "spatie/laravel-medialibrary": "^11.0.0", "spatie/laravel-permission": "^6.0", "spatie/laravel-translatable": "^6.0", "stripe/stripe-php": "^17.3", "webpatser/laravel-countries": "^1.0", "yajra/laravel-datatables": "^11.0", "yajra/laravel-datatables-oracle": "^11.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.16", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"files": ["app/Helpers/Helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Home services\\Admin\\": "packages/Admin/src", "Home services\\User\\": "packages/User/src", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}