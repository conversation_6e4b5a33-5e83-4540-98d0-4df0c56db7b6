<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */
    'otp' => 'OTP',
    'otp_sent' => 'OTP Sent',
    'email' => 'Email',
    'failed' => 'These credentials do not match our records.',
    'password' => 'The provided password is incorrect.',
    'throttle' => 'Too many login attempts. Please try again in :seconds seconds.',
    'confirm_password' => 'Confirm Password',
    'forgot' => 'Forgot ?',
    'forgot_password' => 'Forgot Password',
    'send_reset_password_link' => 'Send Password Reset Link',
    'reset_password' => 'Reset Password',
    'reset_Password_message' => 'Welcome To Fixit, Please Create your Password',
    'login_page_message' => 'Welcome To Fixit, Please Sign in With Your Personal Account Information.',
    'sign_in' => 'Sign In',
    'verify_page_message' => 'Verify Your Email Address',
    'sent_verification_link_msg' => 'A fresh verification link has been sent to your email address.',
    'check_verification_link_msg' => 'Before proceeding, please check your email for a verification link.',
    'not_recieved__email_msg' => 'If you did not receive the email',
    'request_another_code' => 'click here to request another',
    'sent_verification_code_msg' => 'We have sent the verification code in registered detailed!',
    'invalid_email_phone_or_token' => 'Invalid email, phone, or token. Please check and try again..',
    'password_has_been_changed' => 'Your password has been changed!',
    'invalid_otp_or_email' => 'Invalid OTP or Email-id',
    'invalid_otp_or_phone' => 'Invalid OTP or Phone',
    'user_not_exists' => 'This user not exists or deactivate',
    'user_inactive' => 'This user is inactivate',
    'user_deleted' => 'User deleted successfully',

    // new keys
    'password_confirmation' => 'Password confirmation',
    'phone' => 'Phone',
    'token_invalid' => 'Selected Access token is Invalid',
    'logged_out' => 'Successfully logged out.',
    'user_deactivated' => 'This user is deactivated',

    // Email verification keys
    'verification_email_sent' => 'Verification email has been sent to your email address.',
    'verification_email_resent' => 'Verification email has been resent to your email address.',
    'email_verified_successfully' => 'Email verified successfully.',
    'email_already_verified' => 'Email address is already verified.',
    'invalid_verification_token' => 'Invalid verification token.',
    'verification_token_expired' => 'Verification token has expired. Please request a new one.',
];
