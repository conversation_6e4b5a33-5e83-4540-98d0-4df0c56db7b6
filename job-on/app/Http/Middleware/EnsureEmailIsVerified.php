<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureEmailIsVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::guard('api')->user();

        // If user is not authenticated, let other middleware handle it
        if (!$user) {
            return $next($request);
        }

        // Check if email is verified
        if (!$user->hasVerifiedEmail()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'EMAIL_NOT_VERIFIED',
                    'message' => 'Your email address is not verified. Please verify your email to continue.',
                    'email_verification_required' => true,
                ],
                'data' => [
                    'user_email' => $user->email,
                    'verification_endpoints' => [
                        'send_verification' => '/api/auth/send-verification-email',
                        'resend_verification' => '/api/auth/resend-verification-email',
                    ]
                ]
            ], 403);
        }

        return $next($request);
    }
}
