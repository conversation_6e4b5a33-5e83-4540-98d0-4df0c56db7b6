<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\AssetService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Asset;

class AssetController extends Controller
{
    protected $assetService;

    public function __construct(AssetService $assetService)
    {
        $this->assetService = $assetService;
    }

    public function upload(Request $request)
    {
        try {
            $request->validate([
                'files' => 'required|array',
                'files.*' => 'required|file|max:10240',
                'collection' => 'nullable|string'
            ]);
    
            $assets = [];
            $collection = $request->input('collection', 'default');
            
            foreach ($request->file('files') as $file) {
                $asset = $this->assetService->upload(
                    $file,
                    $collection,
                    [
                        'uploaded_by' => auth()->id() ?? null,
                        'original_name' => $file->getClientOriginalName()
                    ]
                );
    
                $assets[] = [
                    'uuid' => $asset->uuid,
                    'file_name' => $asset->file_name,
                    'url' => $asset->url,
                    'mime_type' => $asset->mime_type,
                    'file_size' => $asset->file_size
                ];
            }
    
            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'data' => $assets
            ]);

        } catch (\Exception $e) {
            Log::error('Asset upload failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'File upload failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function delete($uuid)
    {
        try {
            $asset = Asset::where('uuid', $uuid)->firstOrFail();
            $this->assetService->delete($asset);

            return response()->json([
                'success' => true,
                'message' => 'Asset deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Asset deletion failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Asset deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}