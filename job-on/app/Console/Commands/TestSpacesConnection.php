<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestSpacesConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'spaces:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Digital Ocean Spaces connection and basic operations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Digital Ocean Spaces connection...');

        try {
            // Test if spaces disk is configured
            $disk = Storage::disk('spaces');
            $this->info('✓ Spaces disk configured successfully');

            // Test file upload
            $testContent = 'Hello World - ' . date('Y-m-d H:i:s');
            $testFile = 'test-' . time() . '.txt';

            $result = $disk->put($testFile, $testContent);

            if ($result) {
                $this->info("✓ File uploaded successfully: {$testFile}");

                // Test file retrieval
                $retrieved = $disk->get($testFile);
                if ($retrieved === $testContent) {
                    $this->info('✓ File content verified correctly');
                } else {
                    $this->error('✗ File content mismatch');
                    return 1;
                }

                // Test file deletion
                $deleted = $disk->delete($testFile);
                if ($deleted) {
                    $this->info('✓ File deleted successfully');
                } else {
                    $this->error('✗ Failed to delete file');
                    return 1;
                }

            } else {
                $this->error('✗ Failed to upload file');
                return 1;
            }

            $this->newLine();
            $this->info('🎉 Digital Ocean Spaces connection test completed successfully!');
            $this->info('Your configuration is working correctly.');

            return 0;

        } catch (\Exception $e) {
            $this->error('✗ Error: ' . $e->getMessage());
            $this->error('Please check your Digital Ocean Spaces configuration.');
            return 1;
        }
    }
} 