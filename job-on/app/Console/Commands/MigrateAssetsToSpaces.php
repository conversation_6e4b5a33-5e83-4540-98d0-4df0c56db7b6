<?php

namespace App\Console\Commands;

use App\Models\Asset;
use App\Services\AssetService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MigrateAssetsToSpaces extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assets:migrate-to-spaces {--dry-run : Show what would be migrated without actually migrating}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing assets from local storage to Digital Ocean Spaces';

    protected AssetService $assetService;

    public function __construct(AssetService $assetService)
    {
        parent::__construct();
        $this->assetService = $assetService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting asset migration to Digital Ocean Spaces...');

        // Get all assets currently stored locally
        $localAssets = Asset::where('disk', 'public')->get();
        
        if ($localAssets->isEmpty()) {
            $this->info('No local assets found to migrate.');
            return 0;
        }

        $this->info("Found {$localAssets->count()} assets to migrate.");

        if ($this->option('dry-run')) {
            $this->info('DRY RUN MODE - No actual migration will occur.');
        }

        $progressBar = $this->output->createProgressBar($localAssets->count());
        $progressBar->start();

        $successCount = 0;
        $errorCount = 0;

        foreach ($localAssets as $asset) {
            try {
                if (!$this->option('dry-run')) {
                    $success = $this->assetService->migrateToSpaces($asset);
                    if ($success) {
                        $successCount++;
                    } else {
                        $errorCount++;
                        Log::error("Failed to migrate asset: {$asset->uuid}");
                    }
                } else {
                    $successCount++;
                }
            } catch (\Exception $e) {
                $errorCount++;
                Log::error("Error migrating asset {$asset->uuid}: " . $e->getMessage());
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("Migration completed!");
        $this->info("Successfully migrated: {$successCount} assets");
        
        if ($errorCount > 0) {
            $this->warn("Failed to migrate: {$errorCount} assets");
            $this->info("Check the logs for detailed error information.");
        }

        return 0;
    }
} 