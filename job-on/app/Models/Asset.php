<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Asset extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'uuid',
        'file_name',
        'file_path',
        'file_key',
        'disk',
        'mime_type',
        'file_size',
        'collection_name',
        'custom_properties'
    ];

    protected $casts = [
        'custom_properties' => 'array'
    ];

    public function getUrlAttribute(): string
    {
        // For Digital Ocean Spaces, return the CDN URL for better performance
        if ($this->disk === 'spaces') {
            $cdnUrl = config('filesystems.disks.spaces.url');
            return $cdnUrl . '/' . $this->file_path;
        }
        
        // For local storage, use the standard Laravel URL generation
        return Storage::disk($this->disk)->url($this->file_path);
    }

    public function getFullPathAttribute(): string
    {
        // For Digital Ocean Spaces, return the S3 key
        if ($this->disk === 'spaces') {
            return $this->file_path;
        }
        
        // For local storage, return the full file system path
        return Storage::disk($this->disk)->path($this->file_path);
    }

    public function assetable()
    {
        return $this->morphTo();
    }
}