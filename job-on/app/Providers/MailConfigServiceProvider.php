<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use App\Services\GoogleCloudMailService;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        // Get settings from database or environment
        $mailSettings = [
            'driver'     => env('MAIL_MAILER', 'google_cloud'), // Changed default to google_cloud
            'host'       => env('MAIL_HOST', 'smtp.gmail.com'),
            'port'       => env('MAIL_PORT', 587),
            'username'   => env('MAIL_USERNAME'),
            'password'   => env('MAIL_PASSWORD'),
            'encryption' => env('MAIL_ENCRYPTION', 'tls'),
            'from'       => [
                'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
                'name'    => env('MAIL_FROM_NAME', 'MaidProfit')
            ],
        ];

        // Set mail configuration
        Config::set('mail', array_merge(
            Config::get('mail', []), 
            $mailSettings
        ));

        // Override Laravel's Mail facade to use Google Cloud when configured
        if (Config::get('mail.driver') === 'google_cloud') {
            $this->overrideMailFacade();
        }
    }

    /**
     * Override Laravel's Mail facade to use Google Cloud Mail Service
     */
    private function overrideMailFacade()
    {
        // Extend the Mail facade to use Google Cloud Mail Service
        Mail::extend('google_cloud', function ($config) {
            return new class {
                public function send($view, $data = [], $callback = null)
                {
                    $googleMailService = app(GoogleCloudMailService::class);
                    
                    // Extract email data from Laravel's mail format
                    $message = new \Illuminate\Mail\Message(new \Swift_Message());
                    
                    if ($callback) {
                        $callback($message);
                    }
                    
                    $to = $message->getTo();
                    $subject = $message->getSubject();
                    $from = $message->getFrom();
                    
                    // Get the email content
                    if (is_string($view)) {
                        $htmlContent = view($view, $data)->render();
                    } else {
                        $htmlContent = $view->render();
                    }
                    
                    // Send via Google Cloud
                    $fromEmail = $from ? array_keys($from)[0] : config('mail.from.address');
                    $fromName = $from ? array_values($from)[0] : config('mail.from.name');
                    
                    return $googleMailService->sendHtml(
                        array_keys($to)[0], // First recipient
                        $subject,
                        $htmlContent,
                        $fromName
                    );
                }
            };
        });
    }
}
