<?php

namespace App\Providers;

use App\Services\GoogleCloudMailService;
use Illuminate\Support\ServiceProvider;

class GoogleCloudMailServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(GoogleCloudMailService::class, function ($app) {
            return new GoogleCloudMailService();
        });

        // Bind to a shorter alias for easier access
        $this->app->alias(GoogleCloudMailService::class, 'google.mail');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
} 