<?php

namespace App\Services;

use Google_Client;
use Google_Service_Gmail;
use Google_Service_Gmail_Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class GoogleCloudMailService
{
    private $gmailService;
    private $serviceAccountPath;

    public function __construct()
    {
        $this->serviceAccountPath = storage_path('app/google/service-account.json');
        $this->initializeGmailService();
    }

    /**
     * Initialize Gmail service with service account authentication
     */
    private function initializeGmailService()
    {
        try {
            if (!file_exists($this->serviceAccountPath)) {
                throw new \Exception('Google service account credentials file not found at: ' . $this->serviceAccountPath);
            }

            $client = new Google_Client();
            $client->setAuthConfig($this->serviceAccountPath);
            $client->addScope(Google_Service_Gmail::GMAIL_SEND);
            $client->setSubject(Config::get('mail.from.address')); // Use the configured email as subject

            $this->gmailService = new Google_Service_Gmail($client);
        } catch (\Exception $e) {
            Log::error('Failed to initialize Google Cloud Mail Service: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send email using Google Cloud Gmail API
     *
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param string $fromName
     * @param array $attachments
     * @return bool
     */
    public function send($to, $subject, $body, $fromName = null, $attachments = [])
    {
        try {
            $fromEmail = Config::get('mail.from.address');
            $fromName = $fromName ?: Config::get('mail.from.name', 'MaidProfit');

            $message = $this->createMessage($to, $subject, $body, $fromName, $fromEmail, $attachments);
            
            $sentMessage = $this->gmailService->users_messages->send('me', $message);
            
            Log::info('Email sent successfully via Google Cloud', [
                'message_id' => $sentMessage->getId(),
                'to' => $to,
                'subject' => $subject
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send email via Google Cloud: ' . $e->getMessage(), [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Create Gmail message with proper encoding
     *
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param string $fromName
     * @param string $fromEmail
     * @param array $attachments
     * @return Google_Service_Gmail_Message
     */
    private function createMessage($to, $subject, $body, $fromName, $fromEmail, $attachments = [])
    {
        $boundary = uniqid(rand(), true);
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: multipart/mixed; boundary="' . $boundary . '"',
            'From: ' . $fromName . ' <' . $fromEmail . '>',
            'To: ' . $to,
            'Subject: ' . $subject,
        ];

        $message = '';
        $message .= "--{$boundary}\r\n";
        $message .= "Content-Type: text/html; charset=UTF-8\r\n";
        $message .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $message .= $body . "\r\n\r\n";

        // Add attachments if any
        foreach ($attachments as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $content = file_get_contents($attachment['path']);
                $filename = $attachment['name'] ?? basename($attachment['path']);
                
                $message .= "--{$boundary}\r\n";
                $message .= "Content-Type: " . ($attachment['mime'] ?? 'application/octet-stream') . "; name=\"{$filename}\"\r\n";
                $message .= "Content-Disposition: attachment; filename=\"{$filename}\"\r\n";
                $message .= "Content-Transfer-Encoding: base64\r\n\r\n";
                $message .= chunk_split(base64_encode($content)) . "\r\n";
            }
        }

        $message .= "--{$boundary}--";

        $encodedMessage = rtrim(strtr(base64_encode($message), '+/', '-_'), '=');
        
        $gmailMessage = new Google_Service_Gmail_Message();
        $gmailMessage->setRaw($encodedMessage);
        
        return $gmailMessage;
    }

    /**
     * Send email with HTML content
     *
     * @param string $to
     * @param string $subject
     * @param string $htmlBody
     * @param string $fromName
     * @param array $attachments
     * @return bool
     */
    public function sendHtml($to, $subject, $htmlBody, $fromName = null, $attachments = [])
    {
        return $this->send($to, $subject, $htmlBody, $fromName, $attachments);
    }

    /**
     * Send email with plain text content
     *
     * @param string $to
     * @param string $subject
     * @param string $textBody
     * @param string $fromName
     * @param array $attachments
     * @return bool
     */
    public function sendText($to, $subject, $textBody, $fromName = null, $attachments = [])
    {
        $htmlBody = '<html><body>' . nl2br(htmlspecialchars($textBody)) . '</body></html>';
        return $this->send($to, $subject, $htmlBody, $fromName, $attachments);
    }

    /**
     * Test the Google Cloud Mail service
     *
     * @param string $testEmail
     * @return array
     */
    public function test($testEmail)
    {
        try {
            $result = $this->send(
                $testEmail,
                'Test Email - Google Cloud Mail Service',
                '<h1>Test Email</h1><p>This is a test email sent via Google Cloud Mail Service.</p>',
                'Test Sender'
            );

            return [
                'success' => $result,
                'message' => $result ? 'Test email sent successfully' : 'Failed to send test email'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ];
        }
    }
} 