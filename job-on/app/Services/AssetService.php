<?php

namespace App\Services;

use App\Models\Asset;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AssetService
{
    protected $defaultDisk = 'spaces';

    public function upload(
        UploadedFile $file,
        string $collectionName = null,
        array $customProperties = [],
        string $disk = null
    ): Asset {
        try {
            $fileName = $this->generateFileName($file);
            $filePath = $this->generateFilePath($fileName, $collectionName);
            
            // Use Digital Ocean Spaces by default, fallback to public disk
            $diskName = $disk ?: $this->defaultDisk;
            
            $path = Storage::disk($diskName)->putFileAs(
                dirname($filePath),
                $file,
                basename($filePath)
            );

            if (!$path) {
                throw new \Exception('Failed to upload file');
            }

            return Asset::create([
                'uuid' => (string) Str::uuid(),
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $path,
                'file_key' => (string) Str::uuid(),
                'disk' => $diskName,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'collection_name' => $collectionName,
                'custom_properties' => $customProperties
            ]);

        } catch (\Exception $e) {
            \Log::error('Asset upload failed: ' . $e->getMessage());
            throw $e;
        }
    }

    protected function generateFileName(UploadedFile $file): string
    {
        return Str::uuid() . '.' . $file->getClientOriginalExtension();
    }

    protected function generateFilePath(string $fileName, ?string $collectionName): string
    {
        $basePath = date('Y/m/d');
        if ($collectionName) {
            $basePath = $collectionName . '/' . $basePath;
        }
        return $basePath . '/' . $fileName;
    }

    public function delete(Asset $asset): bool
    {
        if (Storage::disk($asset->disk)->exists($asset->file_path)) {
            Storage::disk($asset->disk)->delete($asset->file_path);
        }
        
        return $asset->delete();
    }

    /**
     * Migrate asset from local storage to Digital Ocean Spaces
     */
    public function migrateToSpaces(Asset $asset): bool
    {
        try {
            // Only migrate if asset is on local storage
            if ($asset->disk !== 'public') {
                return true; // Already on spaces
            }

            $localPath = $asset->file_path;
            $spacesPath = $asset->file_path; // Same path structure

            // Check if file exists locally
            if (!Storage::disk('public')->exists($localPath)) {
                \Log::warning("Local file not found for migration: {$localPath}");
                return false;
            }

            // Upload to Digital Ocean Spaces
            $fileContent = Storage::disk('public')->get($localPath);
            $uploaded = Storage::disk('spaces')->put($spacesPath, $fileContent);

            if (!$uploaded) {
                throw new \Exception('Failed to upload to Digital Ocean Spaces');
            }

            // Update asset record
            $asset->update(['disk' => 'spaces']);

            // Delete local file after successful migration
            Storage::disk('public')->delete($localPath);

            return true;

        } catch (\Exception $e) {
            \Log::error('Asset migration failed: ' . $e->getMessage());
            return false;
        }
    }
}